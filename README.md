# Subtitle Comparison Tool

自動比對 KKTV 和 LINETV 字幕檔案的 Node.js 工具。

## 功能特色

- 從 CSV 檔案讀取待比對的劇集資料
- 自動下載並解析 VTT 字幕檔案
- 使用 LLM API 進行智能字幕內容比對
- 將比對結果寫入 CSV 檔案
- 自動移除已處理的資料
- 完整的錯誤處理和日誌記錄

## 安裝

```bash
# 安裝依賴
yarn install
```

## 設定

1. 複製環境變數範例檔案：
```bash
cp .env.example .env
```

2. 編輯 `.env` 檔案，設定你的 LLM API 金鑰：
```bash
LLM_API_KEY=your-api-key-here
LLM_MODEL=gpt-4
LLM_API_ENDPOINT=https://api.openai.com/v1/chat/completions
```

## 使用方法

### 準備資料檔案

確保 `data/ep_for_subtitle_compare.csv` 檔案存在，格式如下：
```csv
kktv_episode_id,linetv_episode_id,auto_match,have_subtitles,similarity,subtitle_eq,name_eq,duration_eq,compare_log,kktv_name,linetv_name,kktv_duration,linetv_duration,kktv_subtitle,linetv_subtitle
03000761010023,131120023,TRUE,TRUE,0.3,0,1,1,,第23集,第23集,1683.72,1683.7200,https://theater.kktv.com.tw/test1.vtt,https://d3c7rimkq79yfu.cloudfront.net/test1.vtt
```

### 執行比對

```bash
# 設定 API 金鑰並執行
export LLM_API_KEY="your-api-key-here"
yarn start
```

### 輸出結果

比對結果會寫入 `data/compare_logs.csv`，格式如下：
```csv
kktv_episode_id,linetv_episode_id,result,updated_at
03000761010023,131120023,"{\"is_same_episode\": true, \"confidence\": 0.95, \"reason\": \"相同內容\"}",2024-01-01T00:00:00.000Z
```

## 測試

```bash
# 執行所有測試
yarn test

# 執行特定測試
yarn test csvReader.test.js

# 監視模式
yarn test:watch
```

## 專案結構

```
├── src/
│   ├── csvReader.js      # CSV 讀取功能
│   ├── vttExtractor.js   # VTT 檔案擷取功能
│   ├── llmApi.js         # LLM API 呼叫功能
│   ├── csvWriter.js      # CSV 寫入功能
│   ├── csvRemover.js     # 資料移除功能
│   └── main.js           # 主程式流程
├── __tests__/            # 測試檔案
├── data/                 # 資料檔案目錄
├── index.js              # 程式進入點
└── README.md
```

## 工作流程

1. 讀取 `data/ep_for_subtitle_compare.csv` 中 `auto_match=TRUE` 的資料
2. 對每筆資料：
   - 下載 KKTV 和 LINETV 的 VTT 字幕檔案
   - 擷取前 300 字的純文字內容
   - 呼叫 LLM API 進行比對
   - 將結果寫入 `data/compare_logs.csv`
   - 從來源檔案移除已處理的資料
3. 如果發生錯誤，程式會立即中斷

## 錯誤處理

- 檔案不存在或無法讀取
- 網路連線問題
- LLM API 呼叫失敗
- 資料格式錯誤

所有錯誤都會記錄在控制台，並中斷程式執行。

## 開發

本專案使用 TDD (Test-Driven Development) 方法開發，遵循以下原則：

- 先寫測試，再寫實作
- 保持測試覆蓋率
- 小步快跑，頻繁提交
- 分離結構性變更和行為變更

## 授權

MIT License
