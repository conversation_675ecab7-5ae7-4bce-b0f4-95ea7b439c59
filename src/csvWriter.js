import fs from 'fs';
import createCsvWriter from 'csv-writer';

export async function writeCompareResult(filePath, result) {
  // Validate required fields
  const requiredFields = ['kktv_episode_id', 'linetv_episode_id', 'result', 'updated_at'];
  for (const field of requiredFields) {
    if (!result[field]) {
      throw new Error(`Missing required field: ${field}`);
    }
  }

  const fileExists = fs.existsSync(filePath);
  
  const csvWriter = createCsvWriter.createObjectCsvWriter({
    path: filePath,
    header: [
      { id: 'kktv_episode_id', title: 'kktv_episode_id' },
      { id: 'linetv_episode_id', title: 'linetv_episode_id' },
      { id: 'result', title: 'result' },
      { id: 'updated_at', title: 'updated_at' }
    ],
    append: fileExists
  });

  await csvWriter.writeRecords([result]);
}
