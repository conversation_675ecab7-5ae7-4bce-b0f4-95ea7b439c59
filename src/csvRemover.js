import fs from 'fs';
import csv from 'csv-parser';
import createCsvWriter from 'csv-writer';

export async function removeProcessedRecord(filePath, kktvEpisodeId) {
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`);
  }

  const records = [];
  let headers = [];

  return new Promise((resolve, reject) => {
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('headers', (headerList) => {
        headers = headerList;
      })
      .on('data', (data) => {
        if (data.kktv_episode_id !== kktvEpisodeId) {
          records.push(data);
        }
      })
      .on('end', async () => {
        try {
          if (headers.length === 0) {
            resolve();
            return;
          }

          const csvWriter = createCsvWriter.createObjectCsvWriter({
            path: filePath,
            header: headers.map(h => ({ id: h, title: h })),
            append: false
          });

          await csvWriter.writeRecords(records);
          resolve();
        } catch (error) {
          reject(error);
        }
      })
      .on('error', (error) => {
        reject(error);
      });
  });
}
