import fs from 'fs';
import path from 'path';
import { readEpisodeData } from './csvReader.js';
import { extractPartialChars } from './vttExtractor.js';
import { callLlmApi } from './llmApi.js';
import { writeCompareResult } from './csvWriter.js';
import { removeProcessedRecord } from './csvRemover.js';

export function validateConfig(config) {
  const requiredFields = ['apiKey', 'model', 'apiEndpoint', 'inputCsvPath', 'outputCsvPath'];
  
  for (const field of requiredFields) {
    if (!config[field]) {
      throw new Error(`Missing required configuration: ${field}`);
    }
  }

  if (!fs.existsSync(config.inputCsvPath)) {
    throw new Error(`Input CSV file not found: ${config.inputCsvPath}`);
  }

  const outputDir = path.dirname(config.outputCsvPath);
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
}

export async function processSubtitleComparison(config) {
  validateConfig(config);

  console.log('Starting subtitle comparison process...');
  
  try {
    const episodes = await readEpisodeData(config.inputCsvPath);
    console.log(`Found ${episodes.length} episodes to process`);

    for (const episode of episodes) {
      console.log(`Processing episode: ${episode.kktv_episode_id}`);
      
      try {
        let result;
        
        if (!episode.kktv_subtitle || !episode.linetv_subtitle) {
          result = 'NA (缺少字幕 URL)';
        } else {
          const kktvText = await extractPartialChars(episode.kktv_subtitle);
          const linetvText = await extractPartialChars(episode.linetv_subtitle);

          if (!kktvText || !linetvText) {
            result = '讀取字幕內容失敗';
          } else {
            result = await callLlmApi(kktvText, linetvText, config);
          }
        }

        const compareResult = {
          kktv_episode_id: episode.kktv_episode_id,
          linetv_episode_id: episode.linetv_episode_id,
          result: result,
          updated_at: new Date().toISOString()
        };

        await writeCompareResult(config.outputCsvPath, compareResult);
        await removeProcessedRecord(config.inputCsvPath, episode.kktv_episode_id);
        
        console.log(`✓ Completed episode: ${episode.kktv_episode_id}`);
        
      } catch (error) {
        console.error(`✗ Error processing episode ${episode.kktv_episode_id}:`, error.message);
        throw error;
      }
    }

    console.log('All episodes processed successfully!');
    
  } catch (error) {
    console.error('Process terminated due to error:', error.message);
    throw error;
  }
}
