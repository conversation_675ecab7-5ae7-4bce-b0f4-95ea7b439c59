import fetch from 'node-fetch';

export function parseVttContent(vttContent) {
  if (!vttContent) {
    return '';
  }

  const lines = vttContent.split('\n');
  let plainText = '';

  for (const line of lines) {
    const trimmedLine = line.trim();

    if (trimmedLine === '' ||
        trimmedLine.startsWith('WEBVTT') ||
        trimmedLine.includes('-->') ||
        trimmedLine.startsWith('::cue')) {
      continue;
    }

    const cleanedLine = trimmedLine.replace(/<[^>]*>/g, '').trim();
    if (cleanedLine) {
      plainText += cleanedLine + ' ';
    }
  }

  return plainText.substring(0, 300);
}

export async function extractPartialChars(url) {
  try {
    const response = await fetch(url);
    const vttContent = await response.text();
    return parseVttContent(vttContent);
  } catch (error) {
    throw error;
  }
}
