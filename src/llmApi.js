import fetch from 'node-fetch';

export function createPrompt(text1, text2) {
  return `
請你扮演一位專業的影片內容分析師。
我會提供兩段從不同來源取得的字幕文字，請判斷它們是否來自同一集的劇情內容。

你的回應必須是純粹的 JSON 格式，不包含任何前後的文字或註解。
JSON 物件需包含以下三個欄位：
1. "is_same_episode": 布林值 (boolean)，如果內容相符則為 true，否則為 false。
2. "confidence": 數值 (number)，一個 0 到 1 之間的小數，表示你的信心指數。
3. "reason": 字串 (string)，用繁體中文簡要說明你判斷的理由（例如：提及了相同的角色名稱、場景或對話）。

---
字幕文字一：
"${text1}"
---
字幕文字二：
"${text2}"
---
  `.trim();
}

export async function callLlmApi(text1, text2, config) {
  const { apiKey, model, apiEndpoint } = config;
  
  const prompt = createPrompt(text1, text2);
  
  const payload = {
    model: model,
    messages: [{ role: 'user', content: prompt }],
    response_format: { type: "json_object" }
  };
  
  const headers = { 
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json'
  };

  const options = {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(payload)
  };

  try {
    const response = await fetch(apiEndpoint, options);
    const responseBody = await response.text();

    if (response.status === 200) {
      const jsonResponse = JSON.parse(responseBody);
      const textContent = jsonResponse.choices[0].message.content;
      return textContent.replace(/```json/g, '').replace(/```/g, '').trim();
    } else {
      throw new Error(`API request failed. Status: ${response.status}, Response: ${responseBody}`);
    }
  } catch (error) {
    throw error;
  }
}
