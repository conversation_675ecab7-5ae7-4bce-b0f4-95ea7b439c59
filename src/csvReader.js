import fs from 'fs';
import csv from 'csv-parser';

export async function readEpisodeData(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    
    if (!fs.existsSync(filePath)) {
      reject(new Error(`File not found: ${filePath}`));
      return;
    }
    
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => {
        if (data.auto_match === 'TRUE') {
          results.push({
            kktv_episode_id: data.kktv_episode_id,
            linetv_episode_id: data.linetv_episode_id,
            auto_match: data.auto_match,
            kktv_subtitle: data.kktv_subtitle,
            linetv_subtitle: data.linetv_subtitle
          });
        }
      })
      .on('end', () => {
        resolve(results);
      })
      .on('error', (error) => {
        reject(error);
      });
  });
}
