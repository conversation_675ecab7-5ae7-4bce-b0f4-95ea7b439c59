import { processSubtitleComparison } from './src/main.js';
import path from 'path';

const config = {
  apiKey: process.env.LLM_API_KEY || '',
  model: process.env.LLM_MODEL || 'gpt-4',
  apiEndpoint: process.env.LLM_API_ENDPOINT || 'https://api.openai.com/v1/chat/completions',
  inputCsvPath: path.join(process.cwd(), 'data', 'ep_for_subtitle_compare.csv'),
  outputCsvPath: path.join(process.cwd(), 'data', 'compare_logs.csv')
};

async function main() {
  try {
    if (!config.apiKey) {
      console.error('Error: LLM_API_KEY environment variable is required');
      console.log('Please set your API key:');
      console.log('export LLM_API_KEY="your-api-key-here"');
      process.exit(1);
    }

    console.log('Subtitle Comparison Tool');
    console.log('========================');
    console.log(`Input file: ${config.inputCsvPath}`);
    console.log(`Output file: ${config.outputCsvPath}`);
    console.log(`Model: ${config.model}`);
    console.log('');

    await processSubtitleComparison(config);
    
    console.log('');
    console.log('Process completed successfully!');
    
  } catch (error) {
    console.error('');
    console.error('Process failed:', error.message);
    process.exit(1);
  }
}

main();
