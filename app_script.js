/**
 * 主函式：從來源工作表讀取資料，比對後將結果更新或插入到目標工作表。
 */
function compareSubtitles(needUpdate) {
    const
        INPUT_SHEET_NAME = 'ep_for_subtitle_compare',
        RESULT_SHEET_NAME = 'subtitle_compare';

    // 在 'ep_map_scored' 工作表中的欄位索引 (A欄為1, B欄為2, ...)
    const
        KKTV_EPISODE_ID_IDX = 1,  // A欄: kktv_episode_id
        LINETV_EPISODE_ID_IDX = 2, // B欄: linetv_episode_id
        NEED_MATCHING_IDX = 3,     // C欄: auto_match checkbox
        KKTV_SUBTITLE_IDX = 14,    // M欄: kktv_subtitle url
        LINETV_SUBTITLE_IDX = KKTV_SUBTITLE_IDX+1;  // N欄: linetv_subtitle url

    // 在 'subtitle_compare' 工作表中的欄位索引
    const
        RESULT_LOG_IDX = 3,        // C欄: compare_log
        RESULT_UPDATED_AT_IDX = 4; // D欄: updated_at
    // --- 結束設定區 ---

    if (!LLM_CONFIG[LLM_PROVIDER].API_KEY) {
        SpreadsheetApp.getUi().alert('錯誤', '請先在程式碼第 20 行填入您的 Gemini API 金鑰。');
        return;
    }

    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const inputSheet = spreadsheet.getSheetByName(INPUT_SHEET_NAME);
    const resultSheet = spreadsheet.getSheetByName(RESULT_SHEET_NAME);

    if (!inputSheet || !resultSheet) {
        SpreadsheetApp.getUi().alert('錯誤', `請確認工作表 '${INPUT_SHEET_NAME}' 和 '${RESULT_SHEET_NAME}' 都存在。`);
        return;
    }

    const startRow = 2;
    const lastRow = inputSheet.getLastRow();

    if (lastRow < startRow) {
        SpreadsheetApp.getUi().alert(`來源工作表 '${INPUT_SHEET_NAME}' 沒有資料可供處理。`);
        return;
    }

    // 建立現有結果的索引，用於後續的 Upsert 操作
    let existingKeys = {};
    if (needUpdate) {
        existingKeys = createKeyIndex(resultSheet);
    }

    const range = inputSheet.getRange(startRow, 1, lastRow - startRow + 1, LINETV_SUBTITLE_IDX);
    const values = range.getValues();

    const ui = SpreadsheetApp.getUi();
    // ui.alert('比對程序即將開始', `將讀取 '${INPUT_SHEET_NAME}' 工作表中已勾選的項目，並將結果更新或插入至 '${RESULT_SHEET_NAME}'。`, ui.ButtonSet.OK);

    for (let i = 0, l = values.length; i < l; i++) {
        const currentRowData = values[i];
        const isChecked = currentRowData[NEED_MATCHING_IDX - 1];

        if (isChecked !== true) {
            continue;
        }

        const kktvEpisodeId = currentRowData[KKTV_EPISODE_ID_IDX - 1];
        const linetvEpisodeId = currentRowData[LINETV_EPISODE_ID_IDX - 1];
        const kktvSubtitleUrl = currentRowData[KKTV_SUBTITLE_IDX - 1];
        const linetvSubtitleUrl = currentRowData[LINETV_SUBTITLE_IDX - 1];

        const currentRowInSheet = startRow + i;
        Logger.log(`正在處理 '${INPUT_SHEET_NAME}' 第 ${currentRowInSheet} 行: ${kktvEpisodeId}`);

        let log;
        if (!kktvSubtitleUrl || !linetvSubtitleUrl) {
            log = 'NA (缺少字幕 URL)';
        } else {
            try {
                const kktvText = extractPartialChars(kktvSubtitleUrl);
                const linetvText = extractPartialChars(linetvSubtitleUrl);

                if (!kktvText || !linetvText) {
                    log = '讀取字幕內容失敗';
                } else {
                    log = callLlmApi(kktvText, linetvText) || 'API 呼叫失敗';
                }
            } catch (e) {
                Logger.log(`處理第 ${currentRowInSheet} 行時發生嚴重錯誤，程式已中止: ${e.toString()}`);
                ui.alert(`執行錯誤: 處理第 ${currentRowInSheet} 行 (${kktvEpisodeId}) 時發生錯誤，程式已中止。\n\n錯誤訊息: ${e.message}`);
                return; // 中止函式執行
            }
        }

        // 執行 Upsert
        const existingRow = existingKeys[kktvEpisodeId];
        const timestamp = new Date();

        if (existingRow) {
            // 更新現有行
            resultSheet.getRange(existingRow, RESULT_LOG_IDX).setValue(log);
            resultSheet.getRange(existingRow, RESULT_UPDATED_AT_IDX).setValue(timestamp);
        } else {
            // 插入新行，並確保 ID 為純文字格式
            resultSheet.appendRow(["'" + kktvEpisodeId.toString(), "'" + linetvEpisodeId.toString(), log, timestamp]);
            // 更新索引，避免在同一次執行中重複插入
            existingKeys[kktvEpisodeId] = resultSheet.getLastRow();
        }

        // Utilities.sleep(650);
    }

    ui.alert(`比對完成！ 所有已勾選的項目已處理完畢，結果已更新至 '${RESULT_SHEET_NAME}'。`);
}

/**
 * 為結果工作表的第一欄 (kktv_episode_id) 建立一個索引。
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet 目標工作表。
 * @returns {Object} 一個將 episode_id 映射到其行號的物件。
 */
function createKeyIndex(sheet) {
    const lastRow = sheet.getLastRow();
    const keys = {};
    if (lastRow < 2) {
        return keys;
    }
    const range = sheet.getRange(2, 1, lastRow - 1, 1);
    const values = range.getValues();

    values.forEach((row, index) => {
        const key = row[0];
        if (key) {
            keys[key] = index + 2; // 行號是 index + startRow (2)
        }
    });

    return keys;
}

/**
 * 從 VTT 檔案 URL 中讀取內容並提取部分純文字字元。
 * @param {string} url VTT 檔案的 URL。
 * @returns {string|null} 提取出的純文字或在失敗時返回 null。
 */
function extractPartialChars(url) {
    try {
        const vttContent = UrlFetchApp.fetch(url, { muteHttpExceptions: true }).getContentText();
        if (!vttContent) return null;

        const lines = vttContent.split('\n');
        let plainText = '';

        for (const line of lines) {
            if (line.trim() === '' || line.trim().startsWith('WEBVTT') || line.includes('-->') || line.startsWith('::cue')) {
                continue;
            }
            plainText += line.replace(/<[^>]*>/g, ' ').trim() + ' ';
        }

        return plainText.substring(0, 300);
    } catch (e) {
        Logger.log(`讀取 URL 失敗: ${url} - 錯誤: ${e.toString()}`);
        return null;
    }
}

/**
 * 呼叫 Gemini API 來比對兩段文字。
 * @param {string} text1 第一段字幕文字。
 * @param {string} text2 第二段字幕文字。
 * @returns {string|null} API 回傳的 JSON 字串或在失敗時返回 null。
 */
function callLlmApi(text1, text2) {
    const currentConfig = LLM_CONFIG[LLM_PROVIDER];
    const apiKey = currentConfig.API_KEY;
    const model = currentConfig.MODEL;
    const url = currentConfig.API_ENDPOINT;


    const prompt = `
    請你扮演一位專業的影片內容分析師。
    我會提供兩段從不同來源取得的字幕文字，請判斷它們是否來自同一集的劇情內容。

    你的回應必須是純粹的 JSON 格式，不包含任何前後的文字或註解。
    JSON 物件需包含以下三個欄位：
    1. "is_same_episode": 布林值 (boolean)，如果內容相符則為 true，否則為 false。
    2. "confidence": 數值 (number)，一個 0 到 1 之間的小數，表示你的信心指數。
    3. "reason": 字串 (string)，用繁體中文簡要說明你判斷的理由（例如：提及了相同的角色名稱、場景或對話）。

    ---
    字幕文字一：
    "${text1}"
    ---
    字幕文字二：
    "${text2}"
    ---
  `;

    const payload = {
        model: model,
        messages: [{ role: 'user', content: prompt }],
        response_format: { type: "json_object" }
    };
    const headers = { 'Authorization': `Bearer ${apiKey}` };

    const options = {
        method: 'post',
        contentType: 'application/json',
        headers: headers,
        payload: JSON.stringify(payload),
        muteHttpExceptions: true
    };

    const response = UrlFetchApp.fetch(url, options);
    const responseCode = response.getResponseCode();
    const responseBody = response.getContentText();

    if (responseCode === 200) {
        const jsonResponse = JSON.parse(responseBody);
        // OpenAI 相容格式的回應直接在 choices[0].message.content 中
        const textContent = jsonResponse.choices[0].message.content;
        // 清理可能存在的 markdown JSON 格式
        return textContent.replace(/```json/g, '').replace(/```/g, '').trim();
    } else {
        Logger.log(`API 錯誤: ${responseCode} - ${responseBody}`);
        throw new Error(`API 請求失敗。狀態碼: ${responseCode}, 回應: ${responseBody}`);
    }
}
