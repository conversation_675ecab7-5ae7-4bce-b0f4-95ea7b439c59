{"name": "subtitle-compare", "version": "1.0.0", "description": "Automated subtitle comparison tool for KKTV and LINETV", "main": "index.js", "scripts": {"start": "node index.js", "test": "node --experimental-vm-modules node_modules/.bin/jest", "test:watch": "node --experimental-vm-modules node_modules/.bin/jest --watch"}, "dependencies": {"csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "node-fetch": "^2.7.0"}, "devDependencies": {"jest": "^29.7.0"}, "type": "module", "keywords": ["subtitle", "comparison", "vtt", "automation"], "author": "", "license": "MIT"}