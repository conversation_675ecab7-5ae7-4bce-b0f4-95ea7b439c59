import { readEpisodeData } from '../src/csvReader.js';
import fs from 'fs';
import path from 'path';

describe('CSV Reader', () => {
  const testDataPath = path.join(process.cwd(), '__tests__', 'test-data');
  const testCsvPath = path.join(testDataPath, 'test-episodes.csv');

  beforeAll(() => {
    if (!fs.existsSync(testDataPath)) {
      fs.mkdirSync(testDataPath, { recursive: true });
    }
    
    const testCsvContent = `kktv_episode_id,linetv_episode_id,auto_match,have_subtitles,similarity,subtitle_eq,name_eq,duration_eq,compare_log,kktv_name,linetv_name,kktv_duration,linetv_duration,kktv_subtitle,linetv_subtitle
03000761010023,131120023,TRUE,TRUE,0.3,0,1,1,,第23集,第23集,1683.72,1683.7200,https://theater.kktv.com.tw/test1.vtt,https://d3c7rimkq79yfu.cloudfront.net/test1.vtt
03000761010024,131120024,TRUE,TRUE,0.3,0,1,1,,第24集,第24集,1851.64,1851.6400,https://theater.kktv.com.tw/test2.vtt,https://d3c7rimkq79yfu.cloudfront.net/test2.vtt`;
    
    fs.writeFileSync(testCsvPath, testCsvContent);
  });

  afterAll(() => {
    if (fs.existsSync(testCsvPath)) {
      fs.unlinkSync(testCsvPath);
    }
  });

  test('should read and parse CSV data correctly', async () => {
    const data = await readEpisodeData(testCsvPath);
    
    expect(data).toHaveLength(2);
    expect(data[0]).toEqual({
      kktv_episode_id: '03000761010023',
      linetv_episode_id: '131120023',
      auto_match: 'TRUE',
      kktv_subtitle: 'https://theater.kktv.com.tw/test1.vtt',
      linetv_subtitle: 'https://d3c7rimkq79yfu.cloudfront.net/test1.vtt'
    });
  });

  test('should filter only auto_match=TRUE records', async () => {
    const testCsvWithMixedData = `kktv_episode_id,linetv_episode_id,auto_match,have_subtitles,similarity,subtitle_eq,name_eq,duration_eq,compare_log,kktv_name,linetv_name,kktv_duration,linetv_duration,kktv_subtitle,linetv_subtitle
03000761010023,131120023,TRUE,TRUE,0.3,0,1,1,,第23集,第23集,1683.72,1683.7200,https://theater.kktv.com.tw/test1.vtt,https://d3c7rimkq79yfu.cloudfront.net/test1.vtt
03000761010024,131120024,FALSE,TRUE,0.3,0,1,1,,第24集,第24集,1851.64,1851.6400,https://theater.kktv.com.tw/test2.vtt,https://d3c7rimkq79yfu.cloudfront.net/test2.vtt`;
    
    const mixedTestPath = path.join(testDataPath, 'mixed-test.csv');
    fs.writeFileSync(mixedTestPath, testCsvWithMixedData);
    
    const data = await readEpisodeData(mixedTestPath);
    
    expect(data).toHaveLength(1);
    expect(data[0].kktv_episode_id).toBe('03000761010023');
    
    fs.unlinkSync(mixedTestPath);
  });

  test('should throw error for non-existent file', async () => {
    await expect(readEpisodeData('non-existent.csv')).rejects.toThrow();
  });
});
