import { processSubtitleComparison } from '../src/main.js';
import fs from 'fs';
import path from 'path';

describe('Integration Test', () => {
  const testDataPath = path.join(process.cwd(), '__tests__', 'test-data');
  const testInputPath = path.join(testDataPath, 'integration-input.csv');
  const testOutputPath = path.join(testDataPath, 'integration-output.csv');

  beforeAll(() => {
    if (!fs.existsSync(testDataPath)) {
      fs.mkdirSync(testDataPath, { recursive: true });
    }
  });

  beforeEach(() => {
    [testInputPath, testOutputPath].forEach(file => {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
      }
    });
  });

  afterAll(() => {
    [testInputPath, testOutputPath].forEach(file => {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
      }
    });
  });

  test('should handle missing subtitle URLs gracefully', async () => {
    const testCsvContent = `kktv_episode_id,linetv_episode_id,auto_match,have_subtitles,similarity,subtitle_eq,name_eq,duration_eq,compare_log,kktv_name,linetv_name,kktv_duration,linetv_duration,kktv_subtitle,linetv_subtitle
03000761010023,131120023,TRUE,TRUE,0.3,0,1,1,,第23集,第23集,1683.72,1683.7200,,`;
    
    fs.writeFileSync(testInputPath, testCsvContent);

    const config = {
      apiKey: 'test-key',
      model: 'gpt-4',
      apiEndpoint: 'https://api.openai.com/v1/chat/completions',
      inputCsvPath: testInputPath,
      outputCsvPath: testOutputPath
    };

    await processSubtitleComparison(config);

    expect(fs.existsSync(testOutputPath)).toBe(true);
    
    const outputContent = fs.readFileSync(testOutputPath, 'utf8');
    expect(outputContent).toContain('NA (缺少字幕 URL)');
    
    const inputContent = fs.readFileSync(testInputPath, 'utf8');
    const inputLines = inputContent.trim().split('\n');
    expect(inputLines).toHaveLength(1); // Only header should remain
  });

  test('should validate configuration before processing', async () => {
    const invalidConfig = {
      // missing required fields
      inputCsvPath: testInputPath,
      outputCsvPath: testOutputPath
    };

    await expect(processSubtitleComparison(invalidConfig))
      .rejects.toThrow('Missing required configuration');
  });

  test('should handle empty input file', async () => {
    fs.writeFileSync(testInputPath, 'kktv_episode_id,linetv_episode_id,auto_match\n');

    const config = {
      apiKey: 'test-key',
      model: 'gpt-4',
      apiEndpoint: 'https://api.openai.com/v1/chat/completions',
      inputCsvPath: testInputPath,
      outputCsvPath: testOutputPath
    };

    await processSubtitleComparison(config);

    expect(fs.existsSync(testOutputPath)).toBe(false);
  });
});
