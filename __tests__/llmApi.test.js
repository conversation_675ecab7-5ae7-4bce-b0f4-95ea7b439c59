import { callLlmApi, createPrompt } from '../src/llmApi.js';

describe('LLM API', () => {
  test('should create correct prompt for subtitle comparison', () => {
    const text1 = '這是第一段字幕內容';
    const text2 = '這是第二段字幕內容';
    
    const prompt = createPrompt(text1, text2);
    
    expect(prompt).toContain(text1);
    expect(prompt).toContain(text2);
    expect(prompt).toContain('is_same_episode');
    expect(prompt).toContain('confidence');
    expect(prompt).toContain('reason');
    expect(prompt).toContain('JSON');
  });

  test('should validate response format', () => {
    const validResponse = {
      is_same_episode: true,
      confidence: 0.95,
      reason: '兩段字幕提及相同的角色和場景'
    };
    
    expect(validResponse).toHaveProperty('is_same_episode');
    expect(validResponse).toHaveProperty('confidence');
    expect(validResponse).toHaveProperty('reason');
    expect(typeof validResponse.is_same_episode).toBe('boolean');
    expect(typeof validResponse.confidence).toBe('number');
    expect(typeof validResponse.reason).toBe('string');
  });

  test('should handle confidence value range', () => {
    const validConfidences = [0, 0.5, 1.0];
    const invalidConfidences = [-0.1, 1.1, 'high'];
    
    validConfidences.forEach(confidence => {
      expect(confidence).toBeGreaterThanOrEqual(0);
      expect(confidence).toBeLessThanOrEqual(1);
    });
    
    invalidConfidences.forEach(confidence => {
      if (typeof confidence === 'number') {
        expect(confidence < 0 || confidence > 1).toBe(true);
      } else {
        expect(typeof confidence).not.toBe('number');
      }
    });
  });

  test('should create API payload correctly', () => {
    const text1 = '測試字幕一';
    const text2 = '測試字幕二';
    const apiKey = 'test-api-key';
    const model = 'gpt-4';
    
    const expectedPayload = {
      model: model,
      messages: [{ 
        role: 'user', 
        content: createPrompt(text1, text2) 
      }],
      response_format: { type: "json_object" }
    };
    
    expect(expectedPayload.model).toBe(model);
    expect(expectedPayload.messages).toHaveLength(1);
    expect(expectedPayload.messages[0].role).toBe('user');
    expect(expectedPayload.response_format.type).toBe('json_object');
  });
});
