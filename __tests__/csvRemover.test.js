import { removeProcessedRecord } from '../src/csvRemover.js';
import fs from 'fs';
import path from 'path';

describe('CSV Remover', () => {
  const testDataPath = path.join(process.cwd(), '__tests__', 'test-data');
  const testCsvPath = path.join(testDataPath, 'test-episodes-remove.csv');

  beforeAll(() => {
    if (!fs.existsSync(testDataPath)) {
      fs.mkdirSync(testDataPath, { recursive: true });
    }
  });

  beforeEach(() => {
    const testCsvContent = `kktv_episode_id,linetv_episode_id,auto_match,have_subtitles,similarity,subtitle_eq,name_eq,duration_eq,compare_log,kktv_name,linetv_name,kktv_duration,linetv_duration,kktv_subtitle,linetv_subtitle
03000761010023,131120023,TRUE,TRUE,0.3,0,1,1,,第23集,第23集,1683.72,1683.7200,https://theater.kktv.com.tw/test1.vtt,https://d3c7rimkq79yfu.cloudfront.net/test1.vtt
03000761010024,131120024,TRUE,TRUE,0.3,0,1,1,,第24集,第24集,1851.64,1851.6400,https://theater.kktv.com.tw/test2.vtt,https://d3c7rimkq79yfu.cloudfront.net/test2.vtt
03000761010025,131120025,TRUE,TRUE,0.3,0,1,1,,第25集,第25集,1900.00,1900.0000,https://theater.kktv.com.tw/test3.vtt,https://d3c7rimkq79yfu.cloudfront.net/test3.vtt`;
    
    fs.writeFileSync(testCsvPath, testCsvContent);
  });

  afterAll(() => {
    if (fs.existsSync(testCsvPath)) {
      fs.unlinkSync(testCsvPath);
    }
  });

  test('should remove specific record by kktv_episode_id', async () => {
    await removeProcessedRecord(testCsvPath, '03000761010024');
    
    const content = fs.readFileSync(testCsvPath, 'utf8');
    const lines = content.trim().split('\n');
    
    expect(lines).toHaveLength(3); // header + 2 remaining records
    expect(content).toContain('03000761010023');
    expect(content).toContain('03000761010025');
    expect(content).not.toContain('03000761010024');
  });

  test('should preserve header when removing records', async () => {
    await removeProcessedRecord(testCsvPath, '03000761010023');
    
    const content = fs.readFileSync(testCsvPath, 'utf8');
    const lines = content.trim().split('\n');
    
    expect(lines[0]).toContain('kktv_episode_id,linetv_episode_id');
  });

  test('should handle non-existent record gracefully', async () => {
    const originalContent = fs.readFileSync(testCsvPath, 'utf8');
    const originalLines = originalContent.trim().split('\n');

    await removeProcessedRecord(testCsvPath, 'non-existent-id');

    const newContent = fs.readFileSync(testCsvPath, 'utf8');
    const newLines = newContent.trim().split('\n');
    expect(newLines).toEqual(originalLines);
  });

  test('should handle empty file', async () => {
    const emptyFilePath = path.join(testDataPath, 'empty.csv');
    fs.writeFileSync(emptyFilePath, '');
    
    await expect(removeProcessedRecord(emptyFilePath, '03000761010023'))
      .resolves.not.toThrow();
    
    fs.unlinkSync(emptyFilePath);
  });

  test('should handle file with only headers', async () => {
    const headerOnlyPath = path.join(testDataPath, 'header-only.csv');
    fs.writeFileSync(headerOnlyPath, 'kktv_episode_id,linetv_episode_id,auto_match\n');

    await removeProcessedRecord(headerOnlyPath, '03000761010023');

    const content = fs.readFileSync(headerOnlyPath, 'utf8');
    expect(content.trim()).toBe('kktv_episode_id,linetv_episode_id,auto_match');

    fs.unlinkSync(headerOnlyPath);
  });

  test('should throw error for non-existent file', async () => {
    await expect(removeProcessedRecord('non-existent.csv', '03000761010023'))
      .rejects.toThrow();
  });
});
