import { extractPartialChars, parseVttContent } from '../src/vttExtractor.js';

describe('VTT Extractor', () => {
  test('should parse VTT content and extract text correctly', () => {
    const mockVttContent = `WEBVTT

00:00:01.000 --> 00:00:03.000
這是第一段字幕內容

00:00:04.000 --> 00:00:06.000
這是第二段字幕內容，包含一些<b>粗體</b>標籤

00:00:07.000 --> 00:00:09.000
這是第三段字幕內容，用來測試擷取功能是否正常運作，應該要能夠正確地移除HTML標籤並且只保留純文字內容。`;

    const result = parseVttContent(mockVttContent);

    expect(result).toBeDefined();
    expect(result.length).toBeLessThanOrEqual(300);
    expect(result).toContain('這是第一段字幕內容');
    expect(result).toContain('這是第二段字幕內容');
    expect(result).not.toContain('<b>');
    expect(result).not.toContain('WEBVTT');
    expect(result).not.toContain('-->');
  });

  test('should handle empty VTT content', () => {
    const result = parseVttContent('');
    expect(result).toBe('');
  });

  test('should limit output to exactly 300 characters', () => {
    const longContent = `WEBVTT

00:00:01.000 --> 00:00:03.000
${'很長的字幕內容'.repeat(100)}`;

    const result = parseVttContent(longContent);
    expect(result.length).toBe(300);
  });

  test('should remove HTML tags correctly', () => {
    const htmlContent = `WEBVTT

00:00:01.000 --> 00:00:03.000
這是<b>粗體</b>和<i>斜體</i>的<span style="color:red">文字</span>`;

    const result = parseVttContent(htmlContent);

    expect(result).toBe('這是粗體和斜體的文字 ');
    expect(result).not.toContain('<');
    expect(result).not.toContain('>');
  });
});
