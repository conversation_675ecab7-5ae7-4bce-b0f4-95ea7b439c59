import { writeCompareResult } from '../src/csvWriter.js';
import fs from 'fs';
import path from 'path';

describe('CSV Writer', () => {
  const testDataPath = path.join(process.cwd(), '__tests__', 'test-data');
  const testLogPath = path.join(testDataPath, 'test-compare-logs.csv');

  beforeAll(() => {
    if (!fs.existsSync(testDataPath)) {
      fs.mkdirSync(testDataPath, { recursive: true });
    }
  });

  beforeEach(() => {
    if (fs.existsSync(testLogPath)) {
      fs.unlinkSync(testLogPath);
    }
  });

  afterAll(() => {
    if (fs.existsSync(testLogPath)) {
      fs.unlinkSync(testLogPath);
    }
  });

  test('should create CSV file with headers if not exists', async () => {
    const result = {
      kktv_episode_id: '03000761010023',
      linetv_episode_id: '131120023',
      result: '{"is_same_episode": true, "confidence": 0.95, "reason": "相同內容"}',
      updated_at: new Date().toISOString()
    };

    await writeCompareResult(testLogPath, result);

    expect(fs.existsSync(testLogPath)).toBe(true);
    
    const content = fs.readFileSync(testLogPath, 'utf8');
    expect(content).toContain('kktv_episode_id,linetv_episode_id,result,updated_at');
    expect(content).toContain(result.kktv_episode_id);
    expect(content).toContain(result.linetv_episode_id);
  });

  test('should append to existing CSV file', async () => {
    const initialContent = 'kktv_episode_id,linetv_episode_id,result,updated_at\n';
    fs.writeFileSync(testLogPath, initialContent);

    const result1 = {
      kktv_episode_id: '03000761010023',
      linetv_episode_id: '131120023',
      result: '{"is_same_episode": true}',
      updated_at: new Date().toISOString()
    };

    const result2 = {
      kktv_episode_id: '03000761010024',
      linetv_episode_id: '131120024',
      result: '{"is_same_episode": false}',
      updated_at: new Date().toISOString()
    };

    await writeCompareResult(testLogPath, result1);
    await writeCompareResult(testLogPath, result2);

    const content = fs.readFileSync(testLogPath, 'utf8');
    const lines = content.trim().split('\n');
    
    expect(lines).toHaveLength(3); // header + 2 data rows
    expect(content).toContain(result1.kktv_episode_id);
    expect(content).toContain(result2.kktv_episode_id);
  });

  test('should handle special characters in result field', async () => {
    const result = {
      kktv_episode_id: '03000761010023',
      linetv_episode_id: '131120023',
      result: '{"reason": "包含逗號,引號\\"和換行\\n的內容"}',
      updated_at: new Date().toISOString()
    };

    await writeCompareResult(testLogPath, result);

    const content = fs.readFileSync(testLogPath, 'utf8');
    expect(content).toContain(result.kktv_episode_id);
  });

  test('should validate required fields', async () => {
    const incompleteResult = {
      kktv_episode_id: '03000761010023'
      // missing required fields
    };

    await expect(writeCompareResult(testLogPath, incompleteResult))
      .rejects.toThrow();
  });
});
