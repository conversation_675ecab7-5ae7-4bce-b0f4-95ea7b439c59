import { processSubtitleComparison, validateConfig } from '../src/main.js';
import fs from 'fs';
import path from 'path';

describe('Main Process', () => {
  const testDataPath = path.join(process.cwd(), '__tests__', 'test-data');
  const testInputPath = path.join(testDataPath, 'test-input.csv');
  const testOutputPath = path.join(testDataPath, 'test-output.csv');

  beforeAll(() => {
    if (!fs.existsSync(testDataPath)) {
      fs.mkdirSync(testDataPath, { recursive: true });
    }
  });

  beforeEach(() => {
    if (fs.existsSync(testOutputPath)) {
      fs.unlinkSync(testOutputPath);
    }
  });

  afterAll(() => {
    [testInputPath, testOutputPath].forEach(file => {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
      }
    });
  });

  test('should validate configuration correctly', () => {
    const validConfig = {
      apiKey: 'test-key',
      model: 'gpt-4',
      apiEndpoint: 'https://api.openai.com/v1/chat/completions',
      inputCsvPath: 'data/ep_for_subtitle_compare.csv',
      outputCsvPath: 'data/compare_logs.csv'
    };

    expect(() => validateConfig(validConfig)).not.toThrow();
  });

  test('should throw error for missing required config', () => {
    const invalidConfig = {
      model: 'gpt-4',
      apiEndpoint: 'https://api.openai.com/v1/chat/completions'
      // missing apiKey
    };

    expect(() => validateConfig(invalidConfig)).toThrow('Missing required configuration');
  });

  test('should validate file paths', () => {
    const configWithInvalidPaths = {
      apiKey: 'test-key',
      model: 'gpt-4',
      apiEndpoint: 'https://api.openai.com/v1/chat/completions',
      inputCsvPath: 'non-existent.csv',
      outputCsvPath: 'data/compare_logs.csv'
    };

    expect(() => validateConfig(configWithInvalidPaths)).toThrow('Input CSV file not found');
  });

  test('should create output directory if not exists', () => {
    const outputDir = path.join(testDataPath, 'new-output-dir');
    const outputPath = path.join(outputDir, 'output.csv');
    
    const config = {
      apiKey: 'test-key',
      model: 'gpt-4',
      apiEndpoint: 'https://api.openai.com/v1/chat/completions',
      inputCsvPath: testInputPath,
      outputCsvPath: outputPath
    };

    // Create test input file
    fs.writeFileSync(testInputPath, 'kktv_episode_id,linetv_episode_id,auto_match\n');

    expect(() => validateConfig(config)).not.toThrow();
    expect(fs.existsSync(outputDir)).toBe(true);

    // Cleanup
    if (fs.existsSync(outputPath)) fs.unlinkSync(outputPath);
    if (fs.existsSync(outputDir)) fs.rmdirSync(outputDir);
  });
});
